"""
Centralized Data Manager for optimized data fetching and caching.

This module provides a centralized approach to data fetching that:
1. Fetches all required data once based on selected date range and client
2. Stores data in temporary DataFrames for reuse across all plots
3. Provides data access methods for different visualization types
4. Implements threading for better performance
"""

import pandas as pd
import streamlit as st
import threading
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from backend.logs.logger_setup import setup_logger
from backend.data.data import (
    get_plant_id, get_plant_display_name,
    get_generation_data_smart_wrapper,
    get_consumption_data_from_csv,
    get_daily_consumption_data
)
from backend.config.tod_config import TOD_SLOTS
from backend.utils.optimized_data_functions import OptimizedDataFunctions

logger = setup_logger('centralized_data_manager', 'centralized_data_manager.log')

class CentralizedDataManager:
    """
    Centralized data manager that fetches all required data once and provides
    access methods for different visualization types.
    """
    
    def __init__(self):
        self.data_cache = {}
        self.cache_lock = threading.Lock()
        self.optimized_functions = OptimizedDataFunctions()
        self.last_fetch_params = None
        self.is_loading = False
        
        # Data storage
        self.generation_data = {}  # plant_name -> DataFrame
        self.consumption_data = {}  # plant_name -> DataFrame
        self.combined_data = {}    # plant_name -> DataFrame
        self.tod_data = {}         # plant_name -> DataFrame
        self.banking_data = {}     # plant_name -> DataFrame
        
        # Metadata
        self.fetch_timestamp = None
        self.selected_client = None
        self.selected_plants = []
        self.date_range = None
        
        logger.info("CentralizedDataManager initialized")
    
    def load_client_data(self) -> Dict:
        """Load client configuration from JSON file."""
        try:
            json_path = os.path.join('src', 'client.json')
            with open(json_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading client data: {e}")
            return {"solar": {}, "wind": {}}
    
    def get_plants_for_client(self, client_name: str) -> List[Dict]:
        """Get all plants for a specific client."""
        client_data = self.load_client_data()
        plants = []
        
        # Get solar plants
        for company, plant_list in client_data.get('solar', {}).items():
            if company == client_name:
                for plant in plant_list:
                    plants.append({
                        'name': plant.get('name'),
                        'plant_id': plant.get('plant_id'),
                        'type': 'solar'
                    })
        
        # Get wind plants
        for company, plant_list in client_data.get('wind', {}).items():
            if company == client_name:
                for plant in plant_list:
                    plants.append({
                        'name': plant.get('name'),
                        'plant_id': plant.get('plant_id'),
                        'type': 'wind'
                    })
        
        return plants
    
    def should_refresh_data(self, client_name: str, selected_plant: str, 
                           start_date: datetime, end_date: datetime) -> bool:
        """Check if data needs to be refreshed based on current parameters."""
        current_params = {
            'client': client_name,
            'plant': selected_plant,
            'start_date': start_date,
            'end_date': end_date
        }
        
        if self.last_fetch_params != current_params:
            self.last_fetch_params = current_params
            return True
        
        # Check if data is older than 5 minutes
        if self.fetch_timestamp:
            time_diff = datetime.now() - self.fetch_timestamp
            if time_diff.total_seconds() > 300:  # 5 minutes
                return True
        
        return False
    
    def fetch_generation_data_threaded(self, plant_info: Dict, start_date: datetime, 
                                     end_date: datetime) -> Tuple[str, pd.DataFrame]:
        """Fetch generation data for a single plant in a separate thread."""
        plant_name = plant_info['name']
        try:
            logger.info(f"Fetching generation data for {plant_name}")
            
            # Determine granularity based on date range
            date_diff = (end_date - start_date).days
            if date_diff <= 1:
                granularity = "15m"  # 15-minute data for single day
            elif date_diff <= 7:
                granularity = "1h"   # Hourly data for week
            else:
                granularity = "1d"   # Daily data for longer periods
            
            df = get_generation_data_smart_wrapper(plant_name, start_date, end_date, granularity)
            
            if df is not None and not df.empty:
                logger.info(f"Successfully fetched {len(df)} rows for {plant_name}")
                return plant_name, df
            else:
                logger.warning(f"No generation data found for {plant_name}")
                return plant_name, pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error fetching generation data for {plant_name}: {e}")
            return plant_name, pd.DataFrame()
    
    def fetch_consumption_data_threaded(self, plant_info: Dict, start_date: datetime, 
                                      end_date: datetime) -> Tuple[str, pd.DataFrame]:
        """Fetch consumption data for a single plant in a separate thread."""
        plant_name = plant_info['name']
        try:
            logger.info(f"Fetching consumption data for {plant_name}")
            
            # Check if single day or date range
            if start_date == end_date:
                df = get_consumption_data_from_csv(plant_name, start_date)
            else:
                df = get_daily_consumption_data(plant_name, start_date, end_date)
            
            if df is not None and not df.empty:
                logger.info(f"Successfully fetched consumption data: {len(df)} rows for {plant_name}")
                return plant_name, df
            else:
                logger.warning(f"No consumption data found for {plant_name}")
                return plant_name, pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error fetching consumption data for {plant_name}: {e}")
            return plant_name, pd.DataFrame()
    
    def process_tod_data(self, plant_name: str, generation_df: pd.DataFrame, 
                        consumption_df: pd.DataFrame) -> pd.DataFrame:
        """Process Time-of-Day binned data from generation and consumption data."""
        try:
            if generation_df.empty and consumption_df.empty:
                return pd.DataFrame()
            
            # Get ToD slots configuration
            tod_slots = TOD_SLOTS
            
            # Use optimized ToD binning
            if not generation_df.empty:
                tod_gen_df = self.optimized_functions.bin_data_to_tod_optimized(generation_df, tod_slots)
            else:
                tod_gen_df = pd.DataFrame()
            
            if not consumption_df.empty:
                tod_cons_df = self.optimized_functions.bin_data_to_tod_optimized(consumption_df, tod_slots)
            else:
                tod_cons_df = pd.DataFrame()
            
            # Combine generation and consumption ToD data
            if not tod_gen_df.empty and not tod_cons_df.empty:
                # Merge on tod_bin
                combined_tod = pd.merge(tod_gen_df, tod_cons_df, on='tod_bin', how='outer', suffixes=('_gen', '_cons'))
                combined_tod = combined_tod.fillna(0)
                
                # Rename columns for consistency
                if 'energy_kwh_gen' in combined_tod.columns:
                    combined_tod = combined_tod.rename(columns={'energy_kwh_gen': 'generation_kwh'})
                if 'energy_kwh_cons' in combined_tod.columns:
                    combined_tod = combined_tod.rename(columns={'energy_kwh_cons': 'consumption_kwh'})
                
                return combined_tod
            elif not tod_gen_df.empty:
                if 'energy_kwh' in tod_gen_df.columns:
                    tod_gen_df = tod_gen_df.rename(columns={'energy_kwh': 'generation_kwh'})
                tod_gen_df['consumption_kwh'] = 0
                return tod_gen_df
            elif not tod_cons_df.empty:
                if 'energy_kwh' in tod_cons_df.columns:
                    tod_cons_df = tod_cons_df.rename(columns={'energy_kwh': 'consumption_kwh'})
                tod_cons_df['generation_kwh'] = 0
                return tod_cons_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error processing ToD data for {plant_name}: {e}")
            return pd.DataFrame()

    def fetch_all_data(self, client_name: str, selected_plant: str,
                      start_date: datetime, end_date: datetime) -> bool:
        """
        Fetch all required data for the selected client/plant and date range.
        Uses threading for parallel data fetching to improve performance.

        Args:
            client_name: Name of the selected client
            selected_plant: Name of the selected plant (or "Combined View")
            start_date: Start date for data fetching
            end_date: End date for data fetching

        Returns:
            bool: True if data was successfully fetched, False otherwise
        """
        try:
            # Check if we need to refresh data
            if not self.should_refresh_data(client_name, selected_plant, start_date, end_date):
                logger.info("Data is up-to-date, skipping fetch")
                return True

            # Set loading state
            self.is_loading = True

            # Clear existing data
            with self.cache_lock:
                self.generation_data.clear()
                self.consumption_data.clear()
                self.combined_data.clear()
                self.tod_data.clear()
                self.banking_data.clear()

            # Determine which plants to fetch data for
            if selected_plant == "Combined View":
                plants_to_fetch = self.get_plants_for_client(client_name)
            else:
                # Get plant info for the selected plant
                all_plants = self.get_plants_for_client(client_name)
                plants_to_fetch = [p for p in all_plants if p['name'] == selected_plant]

            if not plants_to_fetch:
                logger.warning(f"No plants found for client {client_name}, plant {selected_plant}")
                self.is_loading = False
                return False

            logger.info(f"Fetching data for {len(plants_to_fetch)} plants: {[p['name'] for p in plants_to_fetch]}")

            # Use ThreadPoolExecutor for parallel data fetching
            with ThreadPoolExecutor(max_workers=4) as executor:
                # Submit generation data fetch tasks
                generation_futures = {
                    executor.submit(self.fetch_generation_data_threaded, plant, start_date, end_date): plant
                    for plant in plants_to_fetch
                }

                # Submit consumption data fetch tasks
                consumption_futures = {
                    executor.submit(self.fetch_consumption_data_threaded, plant, start_date, end_date): plant
                    for plant in plants_to_fetch
                }

                # Collect generation data results
                for future in as_completed(generation_futures):
                    plant_name, df = future.result()
                    if not df.empty:
                        with self.cache_lock:
                            self.generation_data[plant_name] = df

                # Collect consumption data results
                for future in as_completed(consumption_futures):
                    plant_name, df = future.result()
                    if not df.empty:
                        with self.cache_lock:
                            self.consumption_data[plant_name] = df

            # Process combined data and ToD data for each plant
            for plant in plants_to_fetch:
                plant_name = plant['name']

                # Get generation and consumption data for this plant
                gen_df = self.generation_data.get(plant_name, pd.DataFrame())
                cons_df = self.consumption_data.get(plant_name, pd.DataFrame())

                # Create combined generation vs consumption data
                if not gen_df.empty or not cons_df.empty:
                    combined_df = self.create_combined_data(plant_name, gen_df, cons_df)
                    with self.cache_lock:
                        self.combined_data[plant_name] = combined_df

                # Process ToD data
                tod_df = self.process_tod_data(plant_name, gen_df, cons_df)
                if not tod_df.empty:
                    with self.cache_lock:
                        self.tod_data[plant_name] = tod_df

            # Update metadata
            self.fetch_timestamp = datetime.now()
            self.selected_client = client_name
            self.date_range = (start_date, end_date)

            # Set loading state to False
            self.is_loading = False

            logger.info(f"Successfully fetched data for {len(plants_to_fetch)} plants")
            return True

        except Exception as e:
            logger.error(f"Error in fetch_all_data: {e}")
            self.is_loading = False
            return False

    def create_combined_data(self, plant_name: str, generation_df: pd.DataFrame,
                           consumption_df: pd.DataFrame) -> pd.DataFrame:
        """Create combined generation vs consumption data."""
        try:
            if generation_df.empty and consumption_df.empty:
                return pd.DataFrame()

            # Standardize column names and merge data
            if not generation_df.empty:
                # Ensure we have the right columns
                if 'TOTAL_GENERATION' in generation_df.columns:
                    generation_df = generation_df.rename(columns={'TOTAL_GENERATION': 'generation_kwh'})
                elif 'energy_kwh' in generation_df.columns:
                    generation_df = generation_df.rename(columns={'energy_kwh': 'generation_kwh'})

                # Ensure we have time columns
                if 'DATE' in generation_df.columns:
                    generation_df = generation_df.rename(columns={'DATE': 'date'})
                if 'HOUR' in generation_df.columns:
                    generation_df = generation_df.rename(columns={'HOUR': 'hour'})

            if not consumption_df.empty:
                # Ensure we have the right columns
                if 'energy_kwh' in consumption_df.columns and 'consumption_kwh' not in consumption_df.columns:
                    consumption_df = consumption_df.rename(columns={'energy_kwh': 'consumption_kwh'})

            # Merge based on available time columns
            if not generation_df.empty and not consumption_df.empty:
                # Determine merge keys
                merge_keys = []
                if 'date' in generation_df.columns and 'date' in consumption_df.columns:
                    merge_keys.append('date')
                if 'hour' in generation_df.columns and 'hour' in consumption_df.columns:
                    merge_keys.append('hour')

                if merge_keys:
                    combined_df = pd.merge(generation_df, consumption_df, on=merge_keys, how='outer')
                    combined_df = combined_df.fillna(0)
                else:
                    # If no common time columns, create a simple combination
                    combined_df = generation_df.copy()
                    combined_df['consumption_kwh'] = 0
            elif not generation_df.empty:
                combined_df = generation_df.copy()
                combined_df['consumption_kwh'] = 0
            else:
                combined_df = consumption_df.copy()
                combined_df['generation_kwh'] = 0

            return combined_df

        except Exception as e:
            logger.error(f"Error creating combined data for {plant_name}: {e}")
            return pd.DataFrame()

    # Data Access Methods
    def get_generation_data(self, plant_name: str) -> pd.DataFrame:
        """Get generation data for a specific plant."""
        with self.cache_lock:
            return self.generation_data.get(plant_name, pd.DataFrame()).copy()

    def get_consumption_data(self, plant_name: str) -> pd.DataFrame:
        """Get consumption data for a specific plant."""
        with self.cache_lock:
            return self.consumption_data.get(plant_name, pd.DataFrame()).copy()

    def get_combined_data(self, plant_name: str) -> pd.DataFrame:
        """Get combined generation vs consumption data for a specific plant."""
        with self.cache_lock:
            return self.combined_data.get(plant_name, pd.DataFrame()).copy()

    def get_tod_data(self, plant_name: str) -> pd.DataFrame:
        """Get Time-of-Day binned data for a specific plant."""
        with self.cache_lock:
            return self.tod_data.get(plant_name, pd.DataFrame()).copy()

    def get_combined_wind_solar_data(self, client_name: str) -> pd.DataFrame:
        """Get combined wind and solar generation data for a client."""
        try:
            plants = self.get_plants_for_client(client_name)
            solar_plants = [p for p in plants if p['type'] == 'solar']
            wind_plants = [p for p in plants if p['type'] == 'wind']

            combined_data = []

            # Process solar plants
            for plant in solar_plants:
                plant_name = plant['name']
                gen_df = self.get_generation_data(plant_name)
                if not gen_df.empty:
                    gen_df['plant_type'] = 'Solar'
                    gen_df['plant_name'] = plant_name
                    combined_data.append(gen_df)

            # Process wind plants
            for plant in wind_plants:
                plant_name = plant['name']
                gen_df = self.get_generation_data(plant_name)
                if not gen_df.empty:
                    gen_df['plant_type'] = 'Wind'
                    gen_df['plant_name'] = plant_name
                    combined_data.append(gen_df)

            if not combined_data:
                return pd.DataFrame()

            # Combine all data
            all_data = pd.concat(combined_data, ignore_index=True)

            # Aggregate by date and plant type
            if 'date' in all_data.columns:
                # Ensure we have the right generation column
                gen_col = 'generation_kwh'
                if gen_col not in all_data.columns:
                    if 'TOTAL_GENERATION' in all_data.columns:
                        gen_col = 'TOTAL_GENERATION'
                    elif 'energy_kwh' in all_data.columns:
                        gen_col = 'energy_kwh'

                if gen_col in all_data.columns:
                    result = all_data.groupby(['date', 'plant_type'])[gen_col].sum().reset_index()
                    result = result.pivot(index='date', columns='plant_type', values=gen_col).reset_index()
                    result = result.fillna(0)

                    # Rename columns to match expected format
                    if 'Solar' in result.columns:
                        result = result.rename(columns={'Solar': 'Solar'})
                    if 'Wind' in result.columns:
                        result = result.rename(columns={'Wind': 'Wind'})

                    return result

            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting combined wind/solar data: {e}")
            return pd.DataFrame()

    def get_banking_data(self, plant_name: str, banking_type: str = "daily",
                        tod_based: bool = False) -> pd.DataFrame:
        """Get banking data for a specific plant."""
        # For now, return empty DataFrame as banking logic needs to be implemented
        # This would involve complex calculations based on generation and consumption data
        logger.info(f"Banking data requested for {plant_name} (type: {banking_type}, tod: {tod_based})")
        return pd.DataFrame()

    def is_data_available(self, plant_name: str = None) -> bool:
        """Check if data is available for the specified plant or any plant."""
        with self.cache_lock:
            if plant_name:
                return (plant_name in self.generation_data or
                       plant_name in self.consumption_data or
                       plant_name in self.combined_data)
            else:
                return (bool(self.generation_data) or
                       bool(self.consumption_data) or
                       bool(self.combined_data))

    def get_data_summary(self) -> Dict[str, Any]:
        """Get a summary of available data."""
        with self.cache_lock:
            return {
                'fetch_timestamp': self.fetch_timestamp,
                'selected_client': self.selected_client,
                'date_range': self.date_range,
                'generation_plants': list(self.generation_data.keys()),
                'consumption_plants': list(self.consumption_data.keys()),
                'combined_plants': list(self.combined_data.keys()),
                'tod_plants': list(self.tod_data.keys()),
                'is_loading': self.is_loading
            }

    def clear_cache(self):
        """Clear all cached data."""
        with self.cache_lock:
            self.generation_data.clear()
            self.consumption_data.clear()
            self.combined_data.clear()
            self.tod_data.clear()
            self.banking_data.clear()
            self.fetch_timestamp = None
            self.last_fetch_params = None
        logger.info("Cache cleared")


# Global instance of the centralized data manager
centralized_data_manager = CentralizedDataManager()
